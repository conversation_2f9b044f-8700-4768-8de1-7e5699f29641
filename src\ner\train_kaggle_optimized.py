#!/usr/bin/env python3
"""
Kaggle-optimized training script for Entity Extractor using SpanMarker.
Specifically designed to work within <PERSON><PERSON>'s resource constraints.
"""

import json
import logging
import argparse
import os
import gc
import time
from pathlib import Path
from typing import Dict, List, Any, Optional

# Disable wandb completely
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

# Kaggle-specific optimizations
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["PYTORCH_JIT"] = "0"

# Suppress warnings
import warnings
warnings.filterwarnings("ignore")

import torch
from span_marker import SpanMarkerModel, Trainer as SpanMarkerTrainer
from transformers import TrainingArguments
from datasets import Dataset

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class KaggleNERTrainer:
    """Kaggle-optimized NER trainer with memory and time constraints."""
    
    def __init__(self):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        logger.info(f"Using device: {self.device}")
        
        # Clear GPU cache
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            gpu_memory = torch.cuda.get_device_properties(0).total_memory / (1024**3)
            logger.info(f"GPU memory: {gpu_memory:.1f}GB")
    
    def load_and_prepare_data(self, data_path: str, max_samples: int = 3000) -> List[Dict[str, Any]]:
        """Load and prepare training data with Kaggle optimizations."""
        logger.info(f"Loading data from {data_path} (max {max_samples} samples)")
        
        training_data = []
        with open(data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if len(training_data) >= max_samples:
                    break
                    
                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]
                    
                    # Skip very long texts
                    if len(text.split()) > 80:
                        continue
                    
                    # Convert to BIO format for SpanMarker
                    tokens = text.split()
                    ner_tags = ["O"] * len(tokens)

                    # Calculate character positions for each token
                    token_positions = []
                    char_pos = 0
                    for token in tokens:
                        # Find the start position of this token in the text
                        while char_pos < len(text) and text[char_pos].isspace():
                            char_pos += 1
                        start_pos = char_pos
                        end_pos = start_pos + len(token)
                        token_positions.append((start_pos, end_pos))
                        char_pos = end_pos

                    # Process entities and assign BIO tags
                    for entity in entities:
                        if isinstance(entity, list) and len(entity) >= 3:
                            start_char, end_char, label = entity[0], entity[1], entity[2]

                            # Find tokens that overlap with this entity
                            entity_tokens = []
                            for i, (token_start, token_end) in enumerate(token_positions):
                                # Check if token overlaps with entity span
                                if (token_start < end_char and token_end > start_char):
                                    entity_tokens.append(i)

                            # Assign BIO tags
                            for i, token_idx in enumerate(entity_tokens):
                                if i == 0:
                                    ner_tags[token_idx] = f"B-{label}"
                                else:
                                    ner_tags[token_idx] = f"I-{label}"

                    training_data.append({
                        "tokens": tokens,
                        "ner_tags": ner_tags
                    })
                    
                except Exception as e:
                    logger.warning(f"Error processing line {line_num}: {e}")
                    continue
        
        logger.info(f"Loaded {len(training_data)} samples")
        return training_data
    
    def get_unique_labels(self, data: List[Dict[str, Any]]) -> List[str]:
        """Extract unique labels from BIO tagged data."""
        labels = set()
        for item in data:
            for tag in item["ner_tags"]:
                labels.add(tag)

        labels = sorted(list(labels))
        logger.info(f"Found {len(labels)} unique labels: {labels[:10]}...")
        return labels
    
    def train(self, 
              data_path: str = "data/training/entity_training_data.jsonl",
              output_path: str = "data/models/ner_model_kaggle",
              max_samples: int = 3000,
              epochs: int = 3,
              batch_size: int = 4):
        """Train the model with Kaggle optimizations."""
        
        start_time = time.time()
        
        # Load and prepare data
        train_data = self.load_and_prepare_data(data_path, max_samples)
        if not train_data:
            raise ValueError("No training data loaded")
        
        # Get labels
        labels = self.get_unique_labels(train_data)
        if not labels:
            labels = ["O"]
        
        # Create dataset
        dataset = Dataset.from_list(train_data)
        logger.info(f"Created dataset with {len(dataset)} samples")
        
        # Initialize model with BERT (DistilBERT has token_type_ids issues)
        logger.info("Initializing BERT-based SpanMarker model...")
        try:
            model = SpanMarkerModel.from_pretrained(
                "bert-base-uncased",  # Use BERT instead of DistilBERT
                labels=labels,
                model_max_length=256,  # Reduced sequence length for Kaggle
                entity_max_length=8,   # Reduced entity length
                mean_resizing=False,
                ignore_mismatched_sizes=True
            )
            logger.info("Model initialized successfully")
        except Exception as e:
            logger.error(f"Model initialization failed: {e}")
            raise
        
        # Move to GPU
        if self.device == "cuda":
            model = model.cuda()
            torch.cuda.empty_cache()
        
        # Create output directory
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)
        
        # Kaggle-optimized training arguments
        args = TrainingArguments(
            output_dir=str(output_dir),
            learning_rate=3e-5,  # Slightly higher for faster convergence
            per_device_train_batch_size=batch_size,
            per_device_eval_batch_size=batch_size,
            num_train_epochs=epochs,
            warmup_ratio=0.05,
            weight_decay=0.01,
            # Memory optimizations
            fp16=torch.cuda.is_available(),
            gradient_accumulation_steps=1,
            dataloader_num_workers=0,
            dataloader_pin_memory=False,
            # Minimal saving/evaluation
            save_strategy="no",
            eval_strategy="no",
            logging_steps=200,
            save_total_limit=1,
            load_best_model_at_end=False,
            report_to="none",
            remove_unused_columns=False,
            # Simple scheduler
            lr_scheduler_type="linear",
            warmup_steps=50,
            max_grad_norm=1.0,
            # Stability optimizations
            group_by_length=False,
            dataloader_drop_last=False,
            logging_first_step=False,
            disable_tqdm=False,
            skip_memory_metrics=True,
        )
        
        # Create trainer
        trainer = SpanMarkerTrainer(
            model=model,
            train_dataset=dataset,
            args=args
        )
        
        # Train with error handling
        logger.info(f"Starting training with {epochs} epochs...")
        try:
            trainer.train()
            logger.info("Training completed successfully")
        except Exception as e:
            logger.error(f"Training failed: {e}")
            # Try to save partial model
            try:
                trainer.save_model(str(output_dir))
                logger.info("Saved partial model despite training error")
            except:
                pass
            raise
        
        # Save model
        logger.info(f"Saving model to {output_dir}")
        trainer.save_model(str(output_dir))
        
        # Save metadata
        metadata = {
            "labels": labels,
            "training_samples": len(train_data),
            "epochs": epochs,
            "batch_size": batch_size,
            "training_time": time.time() - start_time,
            "model_type": "bert-spanmarker",
            "kaggle_optimized": True
        }
        
        with open(output_dir / "training_metadata.json", 'w') as f:
            json.dump(metadata, f, indent=2)
        
        # Clean up memory
        del model, trainer, dataset
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
        gc.collect()
        
        logger.info(f"Training completed in {metadata['training_time']:.1f} seconds")
        return metadata

def main():
    """Main function for Kaggle training."""
    parser = argparse.ArgumentParser(description="Kaggle-optimized NER training")
    parser.add_argument("--data-path", type=str, 
                       default="data/training/entity_training_data.jsonl",
                       help="Path to training data")
    parser.add_argument("--output-path", type=str,
                       default="data/models/ner_model_kaggle",
                       help="Output model path")
    parser.add_argument("--max-samples", type=int, default=3000,
                       help="Maximum training samples")
    parser.add_argument("--epochs", type=int, default=3,
                       help="Number of epochs")
    parser.add_argument("--batch-size", type=int, default=4,
                       help="Batch size")
    
    args = parser.parse_args()
    
    try:
        trainer = KaggleNERTrainer()
        result = trainer.train(
            data_path=args.data_path,
            output_path=args.output_path,
            max_samples=args.max_samples,
            epochs=args.epochs,
            batch_size=args.batch_size
        )
        
        print("\n" + "="*50)
        print("🎉 KAGGLE TRAINING COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"📁 Model saved to: {args.output_path}")
        print(f"🏷️  Labels: {len(result['labels'])}")
        print(f"📊 Training samples: {result['training_samples']}")
        print(f"🔄 Epochs: {result['epochs']}")
        print(f"⏱️  Training time: {result['training_time']:.1f}s")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"\n❌ Training failed: {e}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
