#!/usr/bin/env python3
"""
Data validation and preprocessing script for NER training.
Identifies and fixes common data issues that cause training failures.
"""

import json
import logging
import argparse
from pathlib import Path
from typing import Dict, List, Any, Tuple
from collections import Counter
import re

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class NERDataValidator:
    """Validate and fix NER training data."""
    
    def __init__(self):
        self.issues_found = []
        self.stats = {
            'total_samples': 0,
            'valid_samples': 0,
            'invalid_samples': 0,
            'fixed_samples': 0,
            'entity_counts': Counter(),
            'text_lengths': [],
            'entity_lengths': []
        }
    
    def validate_entity_span(self, text: str, entity: List, line_num: int) -> Tuple[bool, List]:
        """Validate and fix entity span."""
        if not isinstance(entity, list) or len(entity) < 3:
            self.issues_found.append(f"Line {line_num}: Invalid entity format: {entity}")
            return False, entity
        
        start, end, label = entity[0], entity[1], entity[2]
        
        # Check data types
        if not isinstance(start, (int, float)) or not isinstance(end, (int, float)):
            self.issues_found.append(f"Line {line_num}: Invalid span types: {type(start)}, {type(end)}")
            return False, entity
        
        start, end = int(start), int(end)
        
        # Check span boundaries
        if start < 0 or end > len(text) or start >= end:
            self.issues_found.append(f"Line {line_num}: Invalid span [{start}, {end}] for text length {len(text)}")
            
            # Try to fix the span
            if start >= end:
                # Swap if reversed
                start, end = min(start, end), max(start, end)
            
            # Clamp to text boundaries
            start = max(0, min(start, len(text) - 1))
            end = max(start + 1, min(end, len(text)))
            
            fixed_entity = [start, end, label]
            self.issues_found.append(f"Line {line_num}: Fixed span to [{start}, {end}]")
            return True, fixed_entity
        
        # Check if span contains valid text
        span_text = text[start:end].strip()
        if not span_text:
            self.issues_found.append(f"Line {line_num}: Empty span [{start}, {end}]")
            return False, entity
        
        # Check label validity
        if not isinstance(label, str) or not label.strip():
            self.issues_found.append(f"Line {line_num}: Invalid label: {label}")
            return False, entity
        
        # Clean label
        clean_label = re.sub(r'[^a-zA-Z0-9_-]', '_', label.strip().lower())
        if clean_label != label:
            fixed_entity = [start, end, clean_label]
            self.issues_found.append(f"Line {line_num}: Cleaned label '{label}' -> '{clean_label}'")
            return True, fixed_entity
        
        return True, entity
    
    def validate_sample(self, data: Dict, line_num: int) -> Tuple[bool, Dict]:
        """Validate and fix a single training sample."""
        if 'text' not in data or 'entities' not in data:
            self.issues_found.append(f"Line {line_num}: Missing required fields")
            return False, data
        
        text = data['text']
        entities = data['entities']
        
        # Validate text
        if not isinstance(text, str) or not text.strip():
            self.issues_found.append(f"Line {line_num}: Invalid or empty text")
            return False, data
        
        # Clean text
        original_text = text
        text = text.strip()
        if len(text) != len(original_text):
            data['text'] = text
            # Adjust entity spans for text changes
            offset = len(original_text) - len(original_text.lstrip())
            for entity in entities:
                if isinstance(entity, list) and len(entity) >= 2:
                    entity[0] = max(0, entity[0] - offset)
                    entity[1] = max(entity[0] + 1, entity[1] - offset)
        
        # Validate entities
        if not isinstance(entities, list):
            self.issues_found.append(f"Line {line_num}: Entities must be a list")
            return False, data
        
        valid_entities = []
        sample_fixed = False
        
        for i, entity in enumerate(entities):
            is_valid, fixed_entity = self.validate_entity_span(text, entity, line_num)
            if is_valid:
                valid_entities.append(fixed_entity)
                if fixed_entity != entity:
                    sample_fixed = True
            else:
                sample_fixed = True
        
        # Update entities
        data['entities'] = valid_entities
        
        # Update statistics
        self.stats['text_lengths'].append(len(text.split()))
        self.stats['entity_lengths'].extend([len(text[e[0]:e[1]].split()) for e in valid_entities if len(e) >= 2])
        
        for entity in valid_entities:
            if len(entity) >= 3:
                self.stats['entity_counts'][entity[2]] += 1
        
        return True, data
    
    def validate_file(self, input_path: str, output_path: str = None, max_samples: int = None) -> Dict[str, Any]:
        """Validate and fix an entire JSONL file."""
        logger.info(f"Validating file: {input_path}")
        
        valid_data = []
        
        with open(input_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if max_samples and len(valid_data) >= max_samples:
                    logger.info(f"Reached max samples limit: {max_samples}")
                    break
                
                self.stats['total_samples'] += 1
                
                try:
                    data = json.loads(line.strip())
                    is_valid, fixed_data = self.validate_sample(data, line_num)
                    
                    if is_valid:
                        valid_data.append(fixed_data)
                        self.stats['valid_samples'] += 1
                        if fixed_data != data:
                            self.stats['fixed_samples'] += 1
                    else:
                        self.stats['invalid_samples'] += 1
                        
                except json.JSONDecodeError as e:
                    self.issues_found.append(f"Line {line_num}: JSON decode error: {e}")
                    self.stats['invalid_samples'] += 1
                except Exception as e:
                    self.issues_found.append(f"Line {line_num}: Unexpected error: {e}")
                    self.stats['invalid_samples'] += 1
        
        # Save cleaned data if output path provided
        if output_path and valid_data:
            logger.info(f"Saving cleaned data to: {output_path}")
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(output_path, 'w', encoding='utf-8') as f:
                for item in valid_data:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
        
        # Generate report
        report = self.generate_report()
        return report
    
    def generate_report(self) -> Dict[str, Any]:
        """Generate validation report."""
        report = {
            'summary': self.stats.copy(),
            'issues': self.issues_found[:50],  # Limit to first 50 issues
            'total_issues': len(self.issues_found)
        }
        
        # Calculate statistics
        if self.stats['text_lengths']:
            report['text_stats'] = {
                'avg_length': sum(self.stats['text_lengths']) / len(self.stats['text_lengths']),
                'max_length': max(self.stats['text_lengths']),
                'min_length': min(self.stats['text_lengths'])
            }
        
        if self.stats['entity_lengths']:
            report['entity_stats'] = {
                'avg_length': sum(self.stats['entity_lengths']) / len(self.stats['entity_lengths']),
                'max_length': max(self.stats['entity_lengths']),
                'min_length': min(self.stats['entity_lengths'])
            }
        
        report['top_entity_types'] = dict(self.stats['entity_counts'].most_common(20))
        
        return report
    
    def print_report(self, report: Dict[str, Any]):
        """Print validation report."""
        print("\n" + "="*60)
        print("📊 NER DATA VALIDATION REPORT")
        print("="*60)
        
        summary = report['summary']
        print(f"📁 Total samples: {summary['total_samples']}")
        print(f"✅ Valid samples: {summary['valid_samples']}")
        print(f"❌ Invalid samples: {summary['invalid_samples']}")
        print(f"🔧 Fixed samples: {summary['fixed_samples']}")
        print(f"📈 Success rate: {(summary['valid_samples']/summary['total_samples']*100):.1f}%")
        
        if 'text_stats' in report:
            stats = report['text_stats']
            print(f"\n📝 Text Statistics:")
            print(f"   Average length: {stats['avg_length']:.1f} words")
            print(f"   Max length: {stats['max_length']} words")
            print(f"   Min length: {stats['min_length']} words")
        
        if 'entity_stats' in report:
            stats = report['entity_stats']
            print(f"\n🏷️  Entity Statistics:")
            print(f"   Average length: {stats['avg_length']:.1f} words")
            print(f"   Max length: {stats['max_length']} words")
            print(f"   Min length: {stats['min_length']} words")
        
        if report['top_entity_types']:
            print(f"\n🔝 Top Entity Types:")
            for entity_type, count in list(report['top_entity_types'].items())[:10]:
                print(f"   {entity_type}: {count}")
        
        if report['total_issues'] > 0:
            print(f"\n⚠️  Issues Found ({report['total_issues']} total):")
            for issue in report['issues'][:10]:
                print(f"   • {issue}")
            if report['total_issues'] > 10:
                print(f"   ... and {report['total_issues'] - 10} more issues")
        
        print("="*60)

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Validate and fix NER training data")
    parser.add_argument("--input", type=str, required=True,
                       help="Input JSONL file path")
    parser.add_argument("--output", type=str,
                       help="Output path for cleaned data")
    parser.add_argument("--max-samples", type=int,
                       help="Maximum samples to process")
    parser.add_argument("--report-only", action="store_true",
                       help="Only generate report, don't save cleaned data")
    
    args = parser.parse_args()
    
    validator = NERDataValidator()
    
    output_path = None if args.report_only else args.output
    report = validator.validate_file(args.input, output_path, args.max_samples)
    
    validator.print_report(report)
    
    # Save report
    if args.output and not args.report_only:
        report_path = Path(args.output).parent / "validation_report.json"
        with open(report_path, 'w') as f:
            json.dump(report, f, indent=2)
        print(f"\n📄 Report saved to: {report_path}")

if __name__ == "__main__":
    main()
