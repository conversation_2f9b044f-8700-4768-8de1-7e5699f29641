import React, { useState } from 'react';
import { AnimatePresence } from 'framer-motion';
import Sidebar from './components/layout/Sidebar';
import Header from './components/layout/Header';
import ChatContainer from './components/chat/ChatContainer';
import EscalationModal from './components/modals/EscalationModal';
import SettingsModal from './components/modals/SettingsModal';
import ChatSearchModal from './components/modals/ChatSearchModal';
import ArchivedChatsModal from './components/modals/ArchivedChatsModal';
import LoginModal from './components/modals/LoginModal';
import RegisterModal from './components/modals/RegisterModal';
import TwoFAModal from './components/modals/TwoFAModal';
import PreLoginUI from './components/auth/PreLoginUI';
import { useAuth } from './hooks/useAuth';
import { useChat } from './hooks/useChat';

const App: React.FC = () => {
  const { user, logout, isLoading, login, verify2FA, register, pendingLogin } = useAuth();
  const {
    messages,
    isLoading: chatLoading,
    attachedFiles,
    chatSessions,
    currentSessionId,
    archivedSessions,
    sendMessage,
    addFileAttachment: addFile,
    removeFileAttachment: removeFile,
    summarizeFile,
    requestClarification: clarificationRequest,
    editMessage,
    createNewSession: handleNewChat,
    loadSession: handleLoadSession,
    deleteSession: handleDeleteSession,
    archiveSession: handleArchiveSession,
    renameSession: handleRenameSession,
    downloadSession: handleDownloadSession,
    escalationTriggered,
    triggerEscalation,
    clearEscalation,
  } = useChat();

  // Modal states
  const [showSettings, setShowSettings] = useState(false);
  const [showSearch, setShowSearch] = useState(false);
  const [showArchivedChats, setShowArchivedChats] = useState(false);
  const [showLogin, setShowLogin] = useState(false);
  const [showRegister, setShowRegister] = useState(false);
  const [show2FA, setShow2FA] = useState(false);
  const [isSidebarCollapsed, setIsSidebarCollapsed] = useState(false);

  const handleToggleSidebar = () => {
    setIsSidebarCollapsed(!isSidebarCollapsed);
  };

  const handleOpenSettings = () => {
    setShowSettings(true);
  };

  const handleLogout = () => {
    logout();
  };

  const handleUpdateUser = (updates: any) => {
    // Implementation for updating user
    console.log('Update user:', updates);
  };

  // Mock functions for features not yet implemented
  const handleOpenVoice = () => {
    // Voice functionality is now handled directly in ChatInput components
    console.log('Voice functionality is embedded in chat input');
  };

  const handleOpenEscalation = () => {
    // setShowEscalation(true); // This state is now managed by ChatContainer
    triggerEscalation();
  };

  const handleOpenSearch = () => {
    setShowSearch(true);
  };

  const handleShowHRTeam = () => {
    // HR Team modal not implemented yet
    console.log('HR Team modal not implemented yet');
  };

  // Mock functions for SettingsModal
  const handleThemeChange = (theme: string) => {
    console.log('Theme changed to:', theme);
  };

  const handleClearAllChats = () => {
    console.log('Clear all chats');
  };

  const handleOpenArchivedChats = () => {
    setShowArchivedChats(true);
  };

  // PreLoginUI handlers
  const handlePreLoginSendMessage = (message: string) => {
    // For pre-login, we can still send messages but they'll be anonymous
    sendMessage(message);
  };

  const handleLogin = () => {
    setShowLogin(true);
  };

  const handleRegister = () => {
    setShowRegister(true);
  };

  // LoginModal handlers
  const handleLoginSubmit = async (email: string, password: string) => {
    return await login(email, password);
  };

  const handleSwitchToRegister = () => {
    setShowLogin(false);
    setShowRegister(true);
  };

  const handleOpen2FA = () => {
    setShowLogin(false);
    setShow2FA(true);
  };

  // RegisterModal handlers
  const handleRegisterSubmit = async (fullName: string, email: string, password: string, employeeId?: string) => {
    return await register(fullName, email, password, employeeId);
  };

  const handleSwitchToLogin = () => {
    setShowRegister(false);
    setShowLogin(true);
  };

  // TwoFAModal handlers
  const handleVerify2FA = async (code: string) => {
    return await verify2FA(code);
  };

  // Show loading state while checking authentication
  if (isLoading) {
    return (
      <div className="flex h-screen items-center justify-center bg-gray-50 dark:bg-gray-900">
        <div className="text-center">
          <div className="w-8 h-8 border-4 border-blue-500 border-t-transparent rounded-full animate-spin mx-auto mb-4"></div>
          <p className="text-gray-600 dark:text-gray-400">Loading...</p>
        </div>
      </div>
    );
  }

  // Show PreLoginUI if user is not authenticated
  if (!user) {
    return (
      <>
        <PreLoginUI
          onLogin={handleLogin}
          onRegister={handleRegister}
          onSendMessage={handlePreLoginSendMessage}
        />
        
        {/* Login Modal */}
        <AnimatePresence>
          {showLogin && (
            <LoginModal
              onClose={() => setShowLogin(false)}
              onLogin={handleLoginSubmit}
              onSwitchToRegister={handleSwitchToRegister}
              onOpen2FA={handleOpen2FA}
            />
          )}
        </AnimatePresence>

        {/* Register Modal */}
        <AnimatePresence>
          {showRegister && (
            <RegisterModal
              onClose={() => setShowRegister(false)}
              onRegister={handleRegisterSubmit}
              onSwitchToLogin={handleSwitchToLogin}
            />
          )}
        </AnimatePresence>

        {/* TwoFA Modal */}
        <AnimatePresence>
          {show2FA && (
            <TwoFAModal
              onClose={() => setShow2FA(false)}
              onVerify={handleVerify2FA}
              email={pendingLogin?.email}
            />
          )}
        </AnimatePresence>
      </>
    );
  }

  // Show main chat interface if user is authenticated
  return (
    <div className="flex h-screen bg-gray-50 dark:bg-gray-900">
      {/* Sidebar */}
      <Sidebar
        isCollapsed={isSidebarCollapsed}
        onToggle={handleToggleSidebar}
        chatSessions={chatSessions}
        currentSessionId={currentSessionId}
        onNewChat={handleNewChat}
        onLoadSession={handleLoadSession}
        onDeleteSession={handleDeleteSession}
        onArchiveSession={handleArchiveSession}
        onRenameSession={handleRenameSession}
        onDownloadSession={handleDownloadSession}
        onOpenSearch={handleOpenSearch}
        onShowHRTeam={handleShowHRTeam}
      />

      {/* Main Content */}
      <div className="flex flex-col flex-1 overflow-hidden">
        {/* Header */}
        <Header
          user={user}
          onOpenSettings={handleOpenSettings}
          onLogout={handleLogout}
        />

        {/* Chat Container */}
        <div className="flex-1 overflow-hidden">
          <ChatContainer
            messages={messages}
            isLoading={chatLoading}
            attachedFiles={attachedFiles}
            onSendMessage={sendMessage}
            onAddFile={addFile}
            onRemoveFile={removeFile}
            onOpenVoice={handleOpenVoice}
            onOpenEscalation={handleOpenEscalation}
            user={user}
            onUpdateUser={handleUpdateUser}
            onClarificationRequest={clarificationRequest}
            onEditMessage={editMessage}
            escalationTriggered={escalationTriggered}
            onClearEscalation={clearEscalation}
          />
        </div>
      </div>

      {/* Modals */}
      <AnimatePresence>
        {showSettings && (
          <SettingsModal
            user={user}
            onClose={() => setShowSettings(false)}
            onUpdateUser={handleUpdateUser}
            theme="light"
            onThemeChange={handleThemeChange}
            onLogout={handleLogout}
            onClearAllChats={handleClearAllChats}
            onOpenArchivedChats={handleOpenArchivedChats}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showSearch && (
          <ChatSearchModal
            isOpen={showSearch}
            onClose={() => setShowSearch(false)}
            chatSessions={chatSessions}
            onSelectChat={handleLoadSession}
          />
        )}
      </AnimatePresence>

      <AnimatePresence>
        {showArchivedChats && (
          <ArchivedChatsModal
            archivedSessions={archivedSessions}
            onClose={() => setShowArchivedChats(false)}
            onRestoreSession={(sessionId) => {
              console.log('Restore session:', sessionId);
              setShowArchivedChats(false);
            }}
          />
        )}
      </AnimatePresence>
    </div>
  );
};

export default App; 