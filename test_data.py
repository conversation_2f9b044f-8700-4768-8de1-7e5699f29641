import json

count = 0
with open('data/training/entity_training_data.jsonl', 'r') as f:
    for line in f:
        count += 1
        if count <= 5:
            data = json.loads(line)
            print(f'Sample {count}: {len(data["text"].split())} words, {len(data["entities"])} entities')
            print(f'Text: {data["text"][:100]}...')
            print(f'Entities: {data["entities"]}')
            print('---')
print(f'Total samples: {count}')
