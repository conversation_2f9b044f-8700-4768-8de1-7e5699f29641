import { useAuthStore } from '../hooks/useAuthStore';

// Simple authFetch wrapper that adds JWT token to requests
export const authFetch = async (url: string, options: RequestInit = {}): Promise<Response> => {
  const token = useAuthStore.getState().token;
  
  const headers = {
    'Content-Type': 'application/json',
    ...options.headers,
  };

  if (token) {
    headers['Authorization'] = `Bearer ${token}`;
  }

  const response = await fetch(url, {
    ...options,
    headers,
  });

  // Handle 401 responses by redirecting to login
  if (response.status === 401) {
    useAuthStore.getState().logout();
    window.location.href = '/login';
  }

  return response;
}; 