{"benefits": ["benefits", "insurance", "health", "dental", "vision", "coverage", "medical benefits", "wellness", "health insurance", "benefit plan", "company benefits", "perks", "401k", "gratuity", "ESOP", "stock options", "retirement plan"], "bonus_issue ": ["bonus not credited", "didn’t get bonus", "bonus missing from payslip", "still waiting for bonus", "my bonus is not added", "bonus gone", "expected bonus not received", "where’s my bonus", "bonus not processed", "bonus issue", "bonus was supposed to be this month", "no bonus received", "bonus not shown in statement", "bonus delay", "bonus pushed to next cycle", "bonus stuck", "HR said bonus is late", "why bonus still not credited", "bonus postponed", "delay in performance bonus", "bonus not processed yet", "bonus not reflecting yet"], "escalation_request": ["please escalate salary issue", "need to raise complaint about salary", "requesting escalation for payroll", "escalate to HR", "raise this issue", "kindly escalate", "forward to concerned team", "take this to manager", "need urgent resolution", "this is serious, escalate please", "I want to talk to <PERSON><PERSON>", "raise a ticket", "submit a complaint", "need help from senior", "take this up", "file escalation", "contact escalation team", "forward this issue", "register complaint", "submit issue to payroll escalation"], "general": ["general", "other", "miscellaneous", "help", "support", "not sure", "don’t know", "random", "something else", "question", "anything", "query", "general enquiry", "ask HR"], "greeting": ["hello", "hi", "hey", "dude", "good morning", "good evening", "greetings", "howdy", "namaste", "yo", "hi there", "hello bot", "what’s up"], "intent_routing": ["show me details of my reimbursement", "how do I update my tax declarations", "I want to check my onboarding tasks", "help me with my internal job transfer", "tell me how to change my bank details", "guide me through the exit process", "update my emergency contact information", "how can I raise a workplace grievance", "tell me about the company's wellness benefits", "what are the steps to request remote work", "how do I nominate a colleague for an award", "what policies apply to contract employees", "I need help with background verification", "update my profile in the HR system", "check my training completion status", "how do I access the holiday calendar", "who is the HRBP for my department", "assign mandatory training to my team", "raise a query regarding relocation", "how do I submit a resignation letter", "what is the probation review process", "help me request for a new ID card", "guide me to submit my address proof", "report a harassment incident", "log a performance concern about a reportee", "what is the grievance redressal mechanism", "I want to check job openings internally", "apply for team member promotion", "how to reset my HR portal password", "who is my reporting manager", "request for job description update", "upload scanned documents to HR", "get the process for policy acknowledgment", "how do I contact the payroll team", "give me the steps to raise a hardware request", "update my gender in employee records", "submit educational documents", "track the progress of my offboarding", "submit supporting docs for reimbursement", "get approval status for policy exemption", "view department-level announcements", "escalate a pending ticket with HR", "register for employee engagement event", "how do I apply for flexible work", "request for team realignment", "cancel my offboarding process", "add emergency contact details", "request for shift change", "how to report late working hours", "ask HR about insurance claim issue", "track my onboarding progress", "request for name correction in records", "log an incident regarding workplace safety", "raise a complaint about supervisor behavior", "report conflict of interest", "get help with tax proof submissions", "ask for clarification on company policies", "check status of my profile verification", "request for duplicate employment letter", "raise an HR query regarding role change", "get documents related to my promotion", "know the list of public holidays", "share feedback on training program", "get assistance for expense filing", "update parental status for benefits", "raise ticket for joining bonus delay", "request training on compliance policies", "file appeal on probation outcome", "ask for team member’s reporting structure", "clarify my compensation structure", "check guidelines for moonlighting policy", "report an access card issue", "ask for L&D team contact", "get clarity on notice period policy", "raise request to update nominee details", "submit form for dependent addition", "request certification for visa processing", "submit referral feedback", "ask HR to unblock system access", "log a lost asset report", "ask for HR help with travel booking", "update my work location", "seek approval for business travel", "get help for loan proof documentation", "check requirements for rehire eligibility", "log ticket for mail alias creation", "ask for update on transfer policy", "get an overview of HR dashboard", "raise a security clearance request", "request support for disability accommodation", "submit feedback on manager appraisal", "know if I’m eligible for WFH", "get assistance on HRMS mobile app", "ask about biometric device issues", "track employee code generation", "report incorrect data in payslip", "log complaint for non-payment of dues", "ask for volunteering program info", "request a new buddy assignment", "get help to enroll in EAP", "ask about non-disclosure agreement process", "update language preference in HR system"], "investment_proof_rejection": ["proofs rejected", "investment proof not accepted", "submitted documents not considered", "I gave LIC but not accepted", "HRA proof denied", "proof rejected without reason", "upload was successful but ignored", "my proofs didn’t go through", "rejection of proofs", "investments rejected"], "irrelevant": ["what's the weather today", "play some music", "tell me a joke", "how tall is Mount Everest", "order me a pizza", "open YouTube", "who won the cricket match", "book a cab for me", "what's 2 plus 2", "turn off the lights", "recommend a movie", "how to make pasta", "what's trending on Twitter", "what's the capital of France", "how do I invest in stocks", "send a message to <PERSON>", "take a screenshot", "remind me at 5 PM", "call my mom", "translate hello to Spanish", "who's the president of India", "what time is it in New York", "give me a pickup line", "how many days until Christmas", "show me nearby restaurants", "start the washing machine", "launch Instagram", "set an alarm for tomorrow", "can you sing", "how to cook biryani", "navigate to the nearest ATM", "how do airplanes fly", "what's your name", "how do I become rich", "what is ChatGPT", "draw a cat", "write a love poem", "simulate a coin toss", "can you dance", "where is my phone", "scan this QR code", "what's the meaning of life", "send an email to my boss", "what does YOLO mean", "show me funny memes", "play relaxing sounds", "open the garage door", "book a hotel room", "who invented Wi-Fi", "how fast is a cheetah", "sing me a lullaby", "do aliens exist", "where is Area 51", "create a workout plan", "what's the time in Tokyo", "solve this riddle", "flip a coin", "do a barrel roll", "count to ten", "make a wish", "can you babysit", "tell me something random", "how old is the universe", "do penguins have knees", "how many stars are in the sky", "show me cat videos", "tell me a bedtime story", "can you do my homework", "how tall is the Eiffel Tower", "give me lottery numbers", "tell me a secret", "what’s your favorite food", "can you paint a picture", "how to win the lottery", "do ghosts exist", "who let the dogs out", "tell me a scary story", "what’s trending today", "open the door", "is water wet", "do you love me", "give me a hug", "beam me up <PERSON><PERSON>", "can you time travel", "how to go viral", "write a novel for me", "is Pluto a planet", "do fish sleep", "how to tame a dragon", "make me laugh", "how to prank my friend", "how do magnets work", "can you hack something", "what does the fox say", "show me unicorns", "can you predict the future", "can I eat cake now", "are zombies real", "when is the apocalypse", "can you play chess"], "leave_enquiry": ["leave balance", "how many leaves do I have", "remaining leaves", "check my leaves", "my leave balance", "paid leave left", "casual leave remaining", "sick leave balance", "how many days off", "my PTO balance", "vacation days left", "personal days remaining", "annual leave balance", "earned leave balance", "unpaid leave balance", "floating holiday balance", "time off accrued", "days I can take off", "my available leave", "how much time off do I have", "my current time off status", "leave quota", "days remaining", "how many vacation days do I have", "current leave status", "my holiday balance", "number of leaves left", "days available to me", "my leave count", "what's my leave", "leave summary", "total leave available", "my leave status", "how much PL do I have", "EL balance", "CL balance", "SL balance", "CO balance", "flexi leave balance", "privilege leave left", "do I have any leave left", "can I take leave", "how much leave can I take", "do I have enough leave", "check sick leave left", "available casual leave", "leave bank status", "paid time off available", "leave accrual till now", "leave details for this year", "how many leaves did I use"], "leave_request": ["leave", "vacation", "time off", "sick", "absence", "pto", "casual leave", "earned leave", "paid leave", "unpaid leave", "holiday", "flexi leave", "optional leave", "comp off", "floating holiday", "SL", "CL", "EL", "CO"], "ner": ["extract EMPLOYEE_NAME and DOCUMENT_TYPE from this uploaded offer letter", "identify DEPARTMENT and POLICY_NAME from the HR announcement", "pull DATE and HR_EVENT from this invitation email", "get EMPLOYEE_NAME and LEAVE_TYPE from this leave request", "highlight DOCUMENT_TYPE and DATE from this attached file", "find POLICY_NAME and BENEFIT_TYPE in this company update", "detect HR_PROCESS and DATE from the training confirmation", "extract EMPLOYEE_NAME and DEPARTMENT from this onboarding form", "identify HR_EVENT and DATE from the announcement", "pull POLICY_NAME and DOCUMENT_TYPE from the uploaded PDF", "get LEAVE_TYPE and EMPLOYEE_NAME from this approval note", "detect DEPARTMENT and HR_EVENT from the internal memo", "extract POLICY_NAME and DATE from the revised handbook", "identify DOCUMENT_TYPE and HR_PROCESS from this scanned form", "highlight BENEFIT_TYPE and DEPARTMENT from this benefits summary", "get EMPLOYEE_NAME and HR_EVENT from the nomination list", "extract HR_PROCESS and DOCUMENT_TYPE from this resume", "pull DATE and POLICY_NAME from the grievance redressal steps", "find DOCUMENT_TYPE and DEPARTMENT in this workflow sheet", "detect EMPLOYEE_NAME and DATE from the training report", "identify POLICY_NAME and HR_PROCESS from this update", "get DOCUMENT_TYPE and HR_EVENT from the shared Google Doc", "pull BENEFIT_TYPE and DATE from the compensation letter", "extract EMPLOYEE_NAME and DEPARTMENT from this promotion letter", "highlight DOCUMENT_TYPE and HR_EVENT in the mail thread", "detect LEAVE_TYPE and DATE from the leave summary", "extract POLICY_NAME and BENEFIT_TYPE from the wellness plan", "find DEPARTMENT and EMPLOYEE_NAME in this feedback file", "identify HR_PROCESS and DATE from this uploaded form", "get DOCUMENT_TYPE and POLICY_NAME from the revised guidelines", "detect EMPLOYEE_NAME and LEAVE_TYPE from the travel request", "highlight DATE and BENEFIT_TYPE from the health insurance summary", "pull HR_EVENT and DOCUMENT_TYPE from this contract", "identify EMPLOYEE_NAME and DEPARTMENT in the appraisal sheet", "extract POLICY_NAME and DATE from the remote work document", "get HR_PROCESS and DOCUMENT_TYPE from the resignation draft", "find EMPL<PERSON>YEE_NAME and DAT<PERSON> from the welcome message", "detect DEPARTMENT and BENEFIT_TYPE from the salary letter", "highlight DOCUMENT_TYPE and HR_PROCESS from this uploaded file", "extract DATE and LEAVE_TYPE from the leave planner", "identify EMPLOYEE_NAME and POLICY_NAME from this complaint", "pull HR_EVENT and DEPARTMENT from the newsletter", "get DOCUMENT_TYPE and DATE from the shared Word file", "extract EMPLOYEE_NAME and HR_PROCESS from the relocation letter", "highlight POLICY_NAME and DEPARTMENT from this job description", "detect HR_EVENT and DOCUMENT_TYPE from the policy update", "identify EMPLOYEE_NAME and DATE from this background check report", "find BENEFIT_TYPE and DEPARTMENT in the expense report", "extract LEAVE_TYPE and DOCUMENT_TYPE from the leave calendar", "get EMPLOYEE_NAME and POLICY_NAME from the grievance form", "pull DATE and HR_EVENT from the offboarding email", "highlight DEPARTMENT and HR_PROCESS from this exit checklist", "detect DOCUMENT_TYPE and DATE from the medical certificate", "extract BENEFIT_TYPE and HR_EVENT from the event poster", "identify POLICY_NAME and DOCUMENT_TYPE from this upload", "get EMPLOYEE_NAME and DATE from the internship offer", "find HR_PROCESS and DEPARTMENT from this internal request", "extract EMPLOYEE_NAME and BENEFIT_TYPE from this claim", "highlight DOCUMENT_TYPE and LEAVE_TYPE from the email thread", "pull DATE and HR_EVENT from the invitation calendar", "identify DEPARTMENT and POLICY_NAME from this update", "detect EMPLOYEE_NAME and DOCUMENT_TYPE in this screenshot", "extract HR_PROCESS and DATE from this reference letter", "get EMPLOYEE_NAME and POLICY_NAME from this compliance doc", "find DOCUMENT_TYPE and HR_EVENT in the audit record", "identify DEPARTMENT and HR_EVENT from the schedule", "highlight DATE and LEAVE_TYPE from the exit mail", "extract POLICY_NAME and DOCUMENT_TYPE from the compliance update", "pull EMPLOYEE_NAME and DEPARTMENT from this uploaded form", "detect HR_PROCESS and POLICY_NAME from the transfer request", "get DOCUMENT_TYPE and DATE from the confirmation form", "identify BENEFIT_TYPE and DEPARTMENT from the reward email", "extract EMPLOYEE_NAME and DOCUMENT_TYPE from the grievance note", "highlight HR_EVENT and POLICY_NAME from the code of conduct", "detect LEAVE_TYPE and DATE from this travel schedule", "get DOCUMENT_TYPE and EMPLOYEE_NAME from the appeal", "pull DEPARTMENT and POLICY_NAME from the change log", "extract DATE and DOCUMENT_TYPE from the new joinee form", "highlight EMPLOYEE_NAME and HR_EVENT from the training invite", "find POLICY_NAME and BENEFIT_TYPE from the benefits slide", "get DOCUMENT_TYPE and LEAVE_TYPE from the email attachment", "extract DEPARTMENT and HR_PROCESS from the submission list", "identify EMPLOYEE_NAME and BENEFIT_TYPE from the flex plan", "detect HR_EVENT and DOCUMENT_TYPE from this PDF file", "highlight POLICY_NAME and DEPARTMENT from the revised rules", "extract DATE and LEAVE_TYPE from the planning mail", "pull DOCUMENT_TYPE and EMPLOYEE_NAME from this letterhead", "find DEPARTMENT and HR_EVENT in the event confirmation", "detect DOCUMENT_TYPE and HR_PROCESS from the transfer letter", "get EMPLOYEE_NAME and DATE from this feedback tracker", "highlight POLICY_NAME and BENEFIT_TYPE from the L&D report", "identify HR_EVENT and DOCUMENT_TYPE from the farewell notice", "pull LEAVE_TYPE and DATE from the shift calendar", "extract EMPLOYEE_NAME and DEPARTMENT from the summary PDF", "get HR_PROCESS and DOCUMENT_TYPE from this experience letter", "highlight DATE and BENEFIT_TYPE from this medical claim form", "find POLICY_NAME and DOCUMENT_TYPE in the uploaded guidelines", "detect EMPLOYEE_NAME and HR_EVENT from the birthday mail", "extract DOCUMENT_TYPE and DATE from the attendance file", "identify DEPARTMENT and BENEFIT_TYPE from the training tracker", "get EMPLOYEE_NAME and POLICY_NAME from the signed document", "pull DOCUMENT_TYPE and HR_PROCESS from this updated form"], "offboarding": ["offboarding", "exit", "termination", "resignation", "last working day", "leaving process", "clearance", "exit formalities", "full and final", "relieving letter", "notice period"], "onboarding": ["onboarding", "orientation", "new hire", "training", "induction", "joining process", "welcome session", "employee onboarding", "joining formalities", "intro session", "first day process"], "payslip_request": ["download payslip", "Download Salary Receipt", "Salary Receipt", "salary slip", "get my payslip", "show me my payslip", "fetch salary document", "get my salary slip", "retrieve payslip", "download salary slip", "my monthly payslip", "salary record", "get my salary document", "send my payslips", "all my payslips", "payslip", "Can I get my payslip?", "I need my salary slip.", "Show my pay stub.", "Where can I find my payslip?", "Access my salary document.", "I'd like to view my payslip.", "Provide my pay statement.", "Can you generate my payslip?", "My pay advice, please.", "Digital payslip", "Digital Salary slip", "Electronic payslip", "Latest payslip", "payslip for March", "payslip for Q1", "payslip for 2023", "payslip from last month", "Q2 salary slip", "payslip for January 2025", "salary slip for June", "get payslip for April 2024", "show me my payslip for last year", "payslip for this quarter", "my payslip for next month", "payslip for the current month", "salary statement for December", "payslip dated 15th July", "Can I get my payslip for the second half of 2024?", "Show me my payslip from last financial year.", "payslip for the month ending June.", "My payslip for the last three months.", "payslip from 2020 to 2022", "show payslip history", "fetch old payslips", "payslip archive", "past salary slips", "history of my payslips", "all my previous payslips", "retrieve historical payslips", "Can I see all payslips from last year?", "Show me my payslip records.", "download my payslip", "email my payslip to me", "export payslip as PDF", "download payslip for tax purposes", "send payslip to my personal email", "Help me download my salary slip.", "Can you share my payslip?", "I'm looking for my payslip.", "Do you have my payslip?", "Could you provide my payslip?", "Where is my most recent payslip?", "access pay history", "give me last month's payslip", "previous month salary slip", "March 2024 payslip", "salary document for June", "view payslip", "get payslip of July", "want to see April payslip", "can you show payslip for Feb", "need my payslip from 2022", "get me annual payslip for 2023"], "policy": ["policy", "guidelines", "rules", "procedures", "company policies", "HR policy", "leave policy", "remote work policy", "compliance", "code of conduct", "travel policy", "expense policy", "dress code", "attendance policy"], "reimbursement_issue": ["reimbursement not paid", "bills uploaded but no payment", "travel claim not received", "expense claim rejected", "mobile bill not processed", "pending reimbursements", "submitted receipts ignored", "reimbursement skipped", "my expenses not reimbursed"], "summarization": ["summarise my leave history for this year", "can you give a summary of my compensation details", "summarise the onboarding steps for new hires", "what are the main points from the remote work policy", "summarise my past payslips", "can you summarise my benefits plan", "provide a summary of exit formalities", "give me a summary of company policies", "summarise feedback from my last performance review", "summarise all open tasks from my HR to-do list", "give a quick summary of the annual appraisal form", "summarise the offer letter into key points", "summarise the offer letter uploaded", "summarise the file uploaded", "summarise this reimbursement claim document", "summarise my training course completions", "give me a summary of attendance for this quarter", "summarise the new workplace conduct policy", "can you summarise this background verification report", "summarise the grievance redressal steps", "summarise all my policy acknowledgments", "summarise my travel claims in one line", "briefly summarise the revised leave rules", "summarise the checklist for employee offboarding", "summarise key HR announcements this month", "can you summarise the L&D report", "summarise the most recent holiday policy update", "give a quick overview of my tax declarations", "summarise the internal transfer process", "summarise the job description of my new role", "summarise this resume into key highlights", "summarise documents submitted for onboarding", "summarise my leave accruals vs usage", "summarise the policy on hybrid work", "provide summary of all signed documents", "summarise my profile from the HR system", "summarise change logs in my employment records", "summarise the health insurance claim form", "summarise completed and pending form submissions", "summarise this performance improvement plan", "give me a summary of my probation feedback", "summarise the content of the uploaded document", "summarise the PDF I just uploaded", "summarise the HR form attached here", "summarise this uploaded resume", "summarise this employee contract file", "summarise this scanned payslip document", "give a summary of the text I pasted", "summarise this long message I received"], "tax_deduction_issue": ["TDS is too high", "extra tax deducted", "tax not calculated properly", "wrong TDS", "why is my tax so much", "deduction under 80C missing", "form 16 doesn't match", "section 80C investment not considered", "LIC not counted", "wrong tax bracket", "taxable income issue", "excess tax this month", "TDS slab problem"], "text_extraction": ["extract leave type and dates from this message", "get the payslip month and amount from this PDF", "extract salary breakup from the document", "pull policy name and its effective date", "get number of earned leaves used in last 6 months", "extract CTC and variable pay from salary slip", "find employee name and resignation date", "get the department and HR process from this mail", "extract joining date and job title from this resume", "get PAN and bank account number from the document", "extract policy clauses related to maternity leave", "get training module names and completion dates", "pull out keywords from this job description", "extract manager name and designation", "get current address from employee form", "extract probation period and evaluation date", "find the grievance ID and category", "extract task deadlines from HR tracker", "get last working day from exit interview form", "extract approved leave dates from the calendar", "get the contact info from this emergency form", "extract skills and certifications from this profile", "pull offer letter issue date and joining deadline", "extract travel expense details from receipt", "get employee ID and payroll number", "extract nomination details from benefits form", "extract training feedback rating and comments", "get all dates mentioned in this leave application", "extract bonus amount from the payslip", "pull claim ID and submitted documents", "extract leave reason and duration", "get marital status and dependents info", "extract job location and office code", "get document type and version number", "extract referred candidate name and status", "get duration of service from this certificate", "extract visa type and validity date", "get policy number and issue date from this form", "extract reviewer name and rating", "extract data from the uploaded file", "pull leave info from this uploaded PDF", "extract details from the attached payslip", "get fields from this uploaded document", "extract personal details from the uploaded HR form", "get named entities from this pasted text", "extract fields from the resume I just uploaded", "pull structured data from the document I sent"], "compensation": ["salary", "pay", "compensation", "bonus", "raise", "salary hike", "increment", "CTC", "pay revision", "performance bonus", "variable pay", "pay grade", "salary band", "total compensation", "gross salary", "net pay", "payout"], "salary_issue": ["salary is low", "my salary is less this month", "got less pay", "CTC mismatch", "net pay is wrong", "didn't get full salary", "basic pay is reduced", "deduction looks wrong", "salary cut issue", "in-hand is low", "this is not my actual pay", "what is this deduction", "payroll error", "pay mismatch", "where is the rest of my salary", "variable pay missing", "incentive not received", "quarterly performance pay not given", "my targets are achieved but no variable", "incentive payout delay", "performance bonus issue", "my variable amount is low", "no incentive shown", "incentive not reflected", "variable pay not credited", "salary breakup not matching", "confused about payslip", "basic and allowance mismatch", "deductions not explained", "wrong CTC split", "breakup in payslip is wrong", "not able to understand salary structure", "this salary structure is different", "my pay components changed", "HRA not showing", "rent not counted", "HRA rejected", "rent receipts uploaded but not accepted", "HRA claim not processed", "HRA missing from salary", "no HRA deduction", "no exemption on rent", "no HRA benefit", "HRA not visible in payslip", "EL encashment not credited", "leave not encashed", "leave payout missing", "pending leave encashment", "leave balance not paid", "leave not settled", "earned leaves not monetized", "EL not shown in salary", "leave encashment skipped"]}