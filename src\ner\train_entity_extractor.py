#!/usr/bin/env python3
"""
Training script for Entity Extractor using SpanMarker.
"""

import json
import logging
import argparse
import os
import random
import numpy as np
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from sklearn.model_selection import train_test_split
from sklearn.metrics import classification_report, f1_score, precision_score, recall_score

# Disable wandb completely
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"

# Fix distributed training issues
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["OMP_NUM_THREADS"] = "1"

# Suppress all warnings
import warnings
warnings.filterwarnings("ignore", category=FutureWarning)
warnings.filterwarnings("ignore", category=DeprecationWarning)
warnings.filterwarnings("ignore", category=UserWarning)
warnings.filterwarnings("ignore", category=RuntimeWarning)
warnings.filterwarnings("ignore", message=".*mean_resizing.*")
warnings.filterwarnings("ignore", message=".*include_inputs_for_metrics.*")
warnings.filterwarnings("ignore", message=".*Trainer.tokenizer.*")
warnings.filterwarnings("ignore", message=".*Using `include_inputs_for_metrics`.*")
warnings.filterwarnings("ignore", message=".*Trainer.tokenizer is now deprecated.*")

# Suppress ONNX warnings
import os
os.environ['PYTORCH_JIT'] = '0'

import torch
from span_marker import SpanMarkerModel, Trainer as SpanMarkerTrainer
from transformers import TrainingArguments
from datasets import Dataset

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(name)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# Suppress span_marker info messages
logging.getLogger("span_marker").setLevel(logging.WARNING)


def get_gpu_memory_gb() -> float:
    """Get available GPU memory in GB."""
    if torch.cuda.is_available():
        return torch.cuda.get_device_properties(0).total_memory / (1024**3)
    else:
        return 0


def validate_entity_spans(text: str, entities: List[List], line_num: int) -> List[List]:
    """
    Validate entity spans and return valid entities.
    
    Args:
        text: Input text
        entities: List of entities
        line_num: Line number for error reporting
    
    Returns:
        List of valid entities
    """
    valid_entities = []
    for entity in entities:
        try:
            if len(entity) >= 3:
                start, end, label = entity[0], entity[1], entity[2]
                
                # Validate span boundaries
                if not (0 <= start < end <= len(text)):
                    logger.warning(f"Invalid span [{start}, {end}] for text length {len(text)} at line {line_num}")
                    continue
                
                # Validate label
                if not isinstance(label, str) or not label.strip():
                    logger.warning(f"Invalid label '{label}' at line {line_num}")
                    continue
                
                valid_entities.append(entity)
            else:
                logger.warning(f"Invalid entity format at line {line_num}: {entity}")
        except Exception as e:
            logger.warning(f"Error validating entity {entity} at line {line_num}: {e}")
    
    return valid_entities


def calculate_metrics(y_true: List[str], y_pred: List[str], labels: List[str]) -> Dict[str, float]:
    """
    Calculate evaluation metrics.
    
    Args:
        y_true: True labels
        y_pred: Predicted labels
        labels: All possible labels
    
    Returns:
        Dictionary of metrics
    """
    # Filter out 'O' labels for entity-specific metrics
    entity_labels = [label for label in labels if label != 'O']
    
    # Calculate overall metrics
    overall_f1 = f1_score(y_true, y_pred, average='weighted', zero_division=0)
    overall_precision = precision_score(y_true, y_pred, average='weighted', zero_division=0)
    overall_recall = recall_score(y_true, y_pred, average='weighted', zero_division=0)
    
    # Calculate entity-specific metrics
    entity_f1 = f1_score(y_true, y_pred, average='weighted', labels=entity_labels, zero_division=0)
    entity_precision = precision_score(y_true, y_pred, average='weighted', labels=entity_labels, zero_division=0)
    entity_recall = recall_score(y_true, y_pred, average='weighted', labels=entity_labels, zero_division=0)
    
    return {
        'overall_f1': overall_f1,
        'overall_precision': overall_precision,
        'overall_recall': overall_recall,
        'entity_f1': entity_f1,
        'entity_precision': entity_precision,
        'entity_recall': entity_recall
    }


def get_dynamic_batch_size(gpu_memory_gb: float, override_batch_size: Optional[int] = None, is_kaggle: bool = False) -> int:
    """
    Determine optimal batch size based on available GPU memory.

    Args:
        gpu_memory_gb: Available GPU memory in GB
        override_batch_size: Optional CLI override
        is_kaggle: Whether running in Kaggle environment

    Returns:
        Optimal batch size
    """
    if override_batch_size is not None:
        logger.info(f"Using CLI override batch size: {override_batch_size}")
        return override_batch_size

    # Conservative batch sizes for Kaggle environment
    if is_kaggle:
        if gpu_memory_gb >= 15:  # Kaggle P100/V100
            batch_size = 4  # More conservative for Kaggle
            logger.info(f"Kaggle GPU detected ({gpu_memory_gb:.1f}GB), using conservative batch_size=4")
        else:
            batch_size = 2  # Very conservative
            logger.info(f"Kaggle GPU detected ({gpu_memory_gb:.1f}GB), using very conservative batch_size=2")
    else:
        # Original logic for local environments
        if gpu_memory_gb >= 12:
            batch_size = 16  # Reduced from 32
            logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB >= 12GB, using batch_size=16")
        elif gpu_memory_gb >= 8:
            batch_size = 8  # Reduced from 16
            logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB (8-12GB), using batch_size=8")
        else:
            batch_size = 4  # Reduced from 8
            logger.info(f"GPU memory: {gpu_memory_gb:.1f}GB < 8GB, using batch_size=4")

    return batch_size

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s | %(name)s:%(lineno)d | %(levelname)-8s | %(funcName)s | PID:%(process)d | TID:%(threadName)s | %(message)s'
)
logger = logging.getLogger(__name__)

# Add warning for CUDA
if not torch.cuda.is_available():
    logger.warning("CUDA not available, using CPU")

def detect_kaggle_environment() -> bool:
    """Detect if running in Kaggle environment."""
    kaggle_indicators = [
        os.path.exists('/kaggle'),
        'KAGGLE_KERNEL_RUN_TYPE' in os.environ,
        'KAGGLE_URL_BASE' in os.environ,
        '/kaggle' in os.getcwd()
    ]
    return any(kaggle_indicators)


class EntityExtractorTrainer:
    """Trainer for SpanMarker-based entity extraction."""

    def __init__(self, base_model: str = "bert-base-uncased"):
        """
        Initialize the trainer.

        Args:
            base_model: Base model to use for training.
        """
        self.base_model = base_model
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.is_kaggle = detect_kaggle_environment()
        logger.info(f"Using device: {self.device}")
        logger.info(f"Kaggle environment detected: {self.is_kaggle}")

        # Use smaller model for Kaggle
        if self.is_kaggle and base_model == "bert-base-uncased":
            self.base_model = "distilbert-base-uncased"  # Smaller, faster model
            logger.info(f"Switched to DistilBERT for Kaggle environment: {self.base_model}")
    
    def load_training_data(self, data_path: str, max_samples: Optional[int] = None) -> List[Dict[str, Any]]:
        """
        Load and convert training data to SpanMarker format.

        Args:
            data_path: Path to JSONL training data file.
            max_samples: Maximum number of samples to load (for Kaggle optimization)

        Returns:
            List of training samples in SpanMarker format with 'tokens' and 'ner_tags' columns.
        """
        training_data = []

        logger.info(f"Loading training data from: {data_path}")

        # Limit samples for Kaggle environment
        if self.is_kaggle and max_samples is None:
            max_samples = 2000  # Limit to 2K samples for Kaggle (reduced from 5K)
            logger.info(f"Kaggle environment: limiting to {max_samples} samples")

        with open(data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                # Break early if we've reached the sample limit
                if max_samples and len(training_data) >= max_samples:
                    logger.info(f"Reached sample limit of {max_samples}, stopping data loading")
                    break

                try:
                    data = json.loads(line.strip())
                    text = data["text"]
                    entities = data["entities"]

                    # Skip very long texts for Kaggle optimization
                    if self.is_kaggle and len(text.split()) > 50:  # Reduced from 100 to 50
                        continue

                    # Handle different entity formats
                    processed_entities = []
                    for entity in entities:
                        if isinstance(entity, list):
                            # Direct list format: [start, end, label]
                            processed_entities.append(entity)
                        elif isinstance(entity, dict) and "value" in entity:
                            # Nested format: {"value": [start, end, label]}
                            if isinstance(entity["value"], list) and len(entity["value"]) >= 3:
                                processed_entities.append(entity["value"])
                        else:
                            logger.warning(f"Unknown entity format at line {line_num}: {entity}")

                    # Validate entity spans
                    valid_entities = validate_entity_spans(text, processed_entities, line_num)

                    # Convert text to tokens and entities to BIO tags
                    tokens, ner_tags = self._convert_to_bio_format(text, valid_entities, line_num)

                    if tokens and ner_tags:
                        training_data.append({
                            "tokens": tokens,
                            "ner_tags": ner_tags
                        })

                except json.JSONDecodeError as e:
                    logger.error(f"JSON decode error at line {line_num}: {e}")
                except KeyError as e:
                    logger.error(f"Missing required key {e} at line {line_num}")
                except Exception as e:
                    logger.error(f"Error processing line {line_num}: {e}")

        logger.info(f"Loaded {len(training_data)} training samples")
        return training_data

    def _convert_to_bio_format(self, text: str, entities: List[List], line_num: int) -> Tuple[List[str], List[str]]:
        """
        Convert text and entities to BIO format.

        Args:
            text: Input text
            entities: List of entities in format [start, end, label]
            line_num: Line number for error reporting

        Returns:
            Tuple of (tokens, ner_tags) in BIO format
        """
        # Simple whitespace tokenization
        tokens = text.split()
        ner_tags = ["O"] * len(tokens)

        # Optimized character position calculation
        token_positions = []
        char_pos = 0
        for token in tokens:
            # Find token position more efficiently
            token_start = text.find(token, char_pos)
            if token_start == -1:  # Fallback if find fails
                while char_pos < len(text) and text[char_pos].isspace():
                    char_pos += 1
                token_start = char_pos
            token_end = token_start + len(token)
            token_positions.append((token_start, token_end))
            char_pos = token_end

        # Process entities and assign BIO tags (optimized)
        for entity in entities:
            try:
                if len(entity) >= 3:
                    # Simplified entity format handling
                    start, end, label = int(entity[0]), int(entity[1]), str(entity[2])

                    # Quick validation
                    if not (0 <= start < end <= len(text)):
                        continue

                    # Optimized token overlap detection
                    for i, (token_start, token_end) in enumerate(token_positions):
                        if token_start < end and token_end > start:
                            # Assign B- tag for first token, I- for subsequent
                            if ner_tags[i] == "O":  # Only assign if not already tagged
                                if any(token_positions[j][0] < end and token_positions[j][1] > start
                                      for j in range(i) if ner_tags[j].endswith(f"-{label}")):
                                    ner_tags[i] = f"I-{label}"
                                else:
                                    ner_tags[i] = f"B-{label}"
            except (ValueError, IndexError, TypeError):
                # Skip invalid entities silently for performance
                continue

        return tokens, ner_tags
    
    def get_unique_labels(self, datasets: List[List[Dict[str, Any]]]) -> List[str]:
        """Extract unique labels from datasets with BIO format."""
        all_labels = set()
        for dataset in datasets:
            for item in dataset:
                for tag in item["ner_tags"]:
                    all_labels.add(tag)

        labels = sorted(list(all_labels))
        logger.info(f"Found {len(labels)} unique entity labels: {labels}")
        return labels
    
    def train(self, 
              training_data_path: str,
              validation_data_path: Optional[str] = None,
              output_path: str = "data/models/ner_model",
              num_epochs: int = 5,
              batch_size: Optional[int] = None,
              learning_rate: float = 5e-5,
              max_length: int = 512,  # Optimized for Kaggle GPU
              entity_max_length: int = 12,
              skip_eval: bool = False,
              test_size: float = 0.2,
              random_state: int = 42):
        """
        Train the SpanMarker model.
        
        Args:
            training_data_path: Path to training data JSONL file.
            validation_data_path: Path to validation data JSONL file.
            output_path: Path to save the trained model.
            num_epochs: Number of training epochs.
            batch_size: Training batch size.
            learning_rate: Learning rate for training.
            max_length: Maximum sequence length.
            entity_max_length: Maximum entity length in words.
        """
        # Load training data with Kaggle optimization
        max_samples = 5000 if self.is_kaggle else None
        train_data = self.load_training_data(training_data_path, max_samples=max_samples)
        logger.info(f"Loaded {len(train_data)} training samples")

        # Handle validation data based on environment
        if self.is_kaggle:
            # For Kaggle, skip validation to save time and memory
            logger.info("Kaggle environment: skipping validation split to optimize training")
            eval_data = None
        else:
            # Split data for validation if no validation data provided
            if validation_data_path and Path(validation_data_path).exists():
                eval_data = self.load_training_data(validation_data_path)
                logger.info(f"Loaded {len(eval_data)} validation samples")
            else:
                logger.info("No validation data provided. Splitting training data for validation.")
                # Split training data into train/validation
                try:
                    # Create stratification labels safely
                    stratify_labels = []
                    for item in train_data:
                        ner_tags = item.get('ner_tags', [])
                        if ner_tags and len(ner_tags) > 0:
                            try:
                                stratify_labels.append(ner_tags[0])
                            except (IndexError, TypeError):
                                stratify_labels.append('O')
                        else:
                            stratify_labels.append('O')

                    # Check if stratification is possible (need at least 2 samples per class)
                    unique_labels = set(stratify_labels)
                    can_stratify = True
                    for label in unique_labels:
                        if stratify_labels.count(label) < 2:
                            can_stratify = False
                            break

                    if can_stratify and len(unique_labels) > 1:
                        train_data, eval_data = train_test_split(
                            train_data,
                            test_size=test_size,
                            random_state=random_state,
                            stratify=stratify_labels
                        )
                    else:
                        logger.info("Using random split (stratification not possible with current data distribution)")
                        train_data, eval_data = train_test_split(
                            train_data,
                            test_size=test_size,
                            random_state=random_state
                        )
                except Exception as e:
                    logger.warning(f"Stratified split failed: {e}. Using random split.")
                    train_data, eval_data = train_test_split(
                        train_data,
                        test_size=test_size,
                        random_state=random_state
                    )
                logger.info(f"Split data: {len(train_data)} training, {len(eval_data)} validation samples")

        # Get unique labels
        datasets = [train_data]
        if eval_data:
            datasets.append(eval_data)
        labels = self.get_unique_labels(datasets)
        
        # Ensure we have at least one label for model initialization
        if not labels:
            logger.warning("No labels found in training data. Adding fallback label 'O' for model initialization.")
            labels = ["O"]
        else:
            # Ensure 'O' label is present for non-entity tokens
            if "O" not in labels:
                labels.append("O")
            # Sort labels safely
            try:
                labels.sort()
            except Exception as e:
                logger.warning(f"Failed to sort labels: {e}. Using original order.")

        # Convert to Hugging Face Dataset objects
        train_dataset = Dataset.from_list(train_data)
        eval_dataset = Dataset.from_list(eval_data) if eval_data else None
        
        # Validate datasets
        if len(train_dataset) == 0:
            raise ValueError("Training dataset is empty")
        if eval_dataset and len(eval_dataset) == 0:
            raise ValueError("Evaluation dataset is empty")
        
        logger.info(f"Training dataset: {len(train_dataset)} samples")
        if eval_dataset:
            logger.info(f"Evaluation dataset: {len(eval_dataset)} samples")
        
        # Determine optimal batch size for environment
        gpu_memory_gb = get_gpu_memory_gb()
        optimal_batch_size = get_dynamic_batch_size(gpu_memory_gb, batch_size, self.is_kaggle)
        logger.info(f"Using batch size: {optimal_batch_size} (Kaggle: {self.is_kaggle})")
        
        # Create model with error handling
        logger.info(f"Initializing SpanMarker model from {self.base_model}")
        logger.info(f"Labels for model: {labels}")
        
        try:
            # Clean labels to ensure they're valid
            cleaned_labels = []
            for label in labels:
                if isinstance(label, str) and label.strip():
                    cleaned_labels.append(label.strip())
                else:
                    logger.warning(f"Skipping invalid label: {label}")
            
            if not cleaned_labels:
                logger.warning("No valid labels found. Using fallback label 'O'")
                cleaned_labels = ["O"]
            
            model = SpanMarkerModel.from_pretrained(
                self.base_model,
                labels=cleaned_labels,
                model_max_length=max_length,
                entity_max_length=entity_max_length,
                mean_resizing=False,  # Disable mean resizing warning
                ignore_mismatched_sizes=True  # Handle size mismatches gracefully
            )
            logger.info(f"Model created successfully with {len(cleaned_labels)} labels")
        except Exception as e:
            logger.error(f"Failed to create model: {e}")
            raise e
        
        # Move to device
        if self.device == "cuda":
            model = model.cuda()
            logger.info("Model moved to GPU")
        
        # Create output directory
        output_dir = Path(output_path)
        output_dir.mkdir(parents=True, exist_ok=True)

        # Optimized training arguments for Kaggle environment
        if self.is_kaggle:
            # Kaggle-optimized settings
            args = TrainingArguments(
                output_dir=str(output_dir),
                learning_rate=learning_rate,
                per_device_train_batch_size=optimal_batch_size,
                per_device_eval_batch_size=optimal_batch_size,
                num_train_epochs=num_epochs,
                warmup_ratio=0.05,  # Reduced warmup for faster training
                weight_decay=0.01,
                # Conservative mixed precision for Kaggle
                fp16=torch.cuda.is_available(),  # Use FP16 for all CUDA devices in Kaggle
                bf16=False,  # Disable BF16 for Kaggle compatibility
                # Memory optimizations for Kaggle
                gradient_checkpointing=False,
                gradient_accumulation_steps=1,  # No accumulation to avoid memory issues
                dataloader_num_workers=0,  # Single-threaded for Kaggle stability
                dataloader_pin_memory=False,  # Disable to save memory
                max_steps=500,  # Limit training steps to prevent timeout
                # Simplified training strategy for Kaggle
                save_strategy="no",  # Don't save intermediate checkpoints
                eval_strategy="no",  # Skip evaluation during training to save time/memory
                logging_steps=50,  # More frequent logging for monitoring
                save_total_limit=1,  # Keep only final model
                load_best_model_at_end=False,  # Skip to save memory
                report_to="none",
                remove_unused_columns=False,
                # Basic optimization only
                lr_scheduler_type="linear",  # Simpler scheduler
                warmup_steps=50,  # Fewer warmup steps
                max_grad_norm=1.0,
                # Disable advanced optimizations for stability
                group_by_length=False,  # Disable for Kaggle stability
                dataloader_drop_last=False,
                # Minimal logging
                logging_first_step=False,
                logging_nan_inf_filter=True,
                # Kaggle-specific optimizations
                skip_memory_metrics=True,
                disable_tqdm=False,  # Keep progress bar for monitoring
            )
        else:
            # Full-featured training arguments for local/cloud environments
            args = TrainingArguments(
                output_dir=str(output_dir),
                learning_rate=learning_rate,
                per_device_train_batch_size=optimal_batch_size,
                per_device_eval_batch_size=optimal_batch_size,
                num_train_epochs=num_epochs,
                warmup_ratio=0.1,
                weight_decay=0.01,
                # Mixed precision for SOTA performance
                bf16=torch.cuda.is_available() and torch.cuda.get_device_capability()[0] >= 8,
                fp16=torch.cuda.is_available() and torch.cuda.get_device_capability()[0] < 8,
                # Memory and performance optimizations
                gradient_checkpointing=False,
                gradient_accumulation_steps=2,
                dataloader_num_workers=4,
                dataloader_pin_memory=True,
                # Training strategy
                save_strategy="epoch",
                eval_strategy="epoch",
                logging_steps=50,
                save_total_limit=3,
                load_best_model_at_end=True,
                metric_for_best_model="eval_loss",
                greater_is_better=False,
                report_to="none",
                remove_unused_columns=False,
                # SOTA hyperparameter optimization
                lr_scheduler_type="cosine",
                warmup_steps=100,
                max_grad_norm=1.0,
                # Advanced optimizations
                group_by_length=True,
                dataloader_drop_last=True,
                # Performance monitoring
                logging_first_step=True,
                logging_nan_inf_filter=True,
            )

        # Create trainer with evaluation dataset
        trainer_kwargs = {
            "model": model,
            "train_dataset": train_dataset,
            "args": args
        }
        
        # Add eval_dataset only if it exists
        if eval_dataset is not None:
            trainer_kwargs["eval_dataset"] = eval_dataset
        
        # Create trainer (tokenizer is handled internally by SpanMarker)
        trainer = SpanMarkerTrainer(**trainer_kwargs)
        
        # Train the model with error handling and monitoring
        logger.info("Starting training...")

        # Add debug info
        logger.info(f"Model type: {type(model)}")
        logger.info(f"Dataset type: {type(train_dataset)}")
        logger.info(f"Dataset length: {len(train_dataset)}")
        logger.info(f"Batch size: {optimal_batch_size}")
        logger.info(f"Epochs: {num_epochs}")

        try:
            # Add progress monitoring
            import time
            start_time = time.time()

            # Custom training with progress monitoring
            trainer.train()

            training_time = time.time() - start_time
            logger.info(f"Training completed successfully in {training_time:.1f} seconds")

        except KeyboardInterrupt:
            logger.warning("Training interrupted by user")
            # Save partial model
            try:
                trainer.save_model(str(output_dir))
                logger.info(f"Partial model saved to {output_dir}")
            except:
                pass
            raise
        except Exception as e:
            logger.error(f"Training failed: {e}")
            import traceback
            logger.error(f"Full traceback: {traceback.format_exc()}")

            # Try to save the model even if training fails
            try:
                logger.info("Attempting to save model despite training error...")
                trainer.save_model(str(output_dir))
                logger.info(f"Model saved to {output_dir} despite training error")
            except Exception as save_error:
                logger.error(f"Failed to save model: {save_error}")
            raise e
        
        # Evaluate the model with proper metrics
        if eval_dataset and not skip_eval:
            logger.info("Evaluating model with comprehensive metrics...")
            try:
                eval_results = trainer.evaluate()
                logger.info(f"Evaluation results: {eval_results}")
                
                # Additional custom evaluation
                logger.info("Running detailed entity-level evaluation...")
                # This would require custom evaluation logic
                # For now, we rely on SpanMarker's built-in evaluation
                
            except Exception as e:
                logger.warning(f"Evaluation failed: {e}. Continuing without evaluation.")
                eval_results = None
        else:
            logger.info("Skipping evaluation as requested")
            eval_results = None
        
        # Save the model
        logger.info(f"Saving model to {output_dir}")
        trainer.save_model(str(output_dir))
        
        # Save entity types for reference
        entity_types_path = output_dir / "entity_types.json"
        with open(entity_types_path, 'w', encoding='utf-8') as f:
            json.dump({
                "entity_types": labels,
                "encoder": self.base_model,
                "total_entity_types": len(labels)
            }, f, indent=2)
        
        logger.info(f"Training completed successfully. Model saved to: {output_dir}")
        
        # Automatic ONNX optimization after training
        logger.info("Starting automatic ONNX optimization...")
        try:
            from .convert_to_onnx import convert_to_onnx
            
            onnx_result = convert_to_onnx(str(output_dir))
            if onnx_result["success"]:
                logger.info(f"✅ ONNX optimization successful!")
                logger.info(f"📊 Compression ratio: {onnx_result['compression_ratio']:.2f}x")
                logger.info(f"📁 ONNX model saved to: {onnx_result['output_path']}")
                onnx_path = onnx_result["output_path"]
            else:
                logger.warning(f"❌ ONNX optimization failed: {onnx_result['error']}")
                onnx_path = None
        except Exception as e:
            logger.warning(f"❌ ONNX optimization failed: {e}")
            onnx_path = None
        
        return {
            "model_path": str(output_dir),
            "entity_types": labels,
            "training_samples": len(train_data),
            "validation_samples": len(eval_data) if eval_data else 0,
            "epochs": num_epochs,
            "onnx_path": onnx_path
        }


def main():
    """Main training function."""
    parser = argparse.ArgumentParser(description="Train Entity Extractor")
    parser.add_argument("--training-data", type=str, 
                       default="data/training/entity_training_data.jsonl",
                       help="Path to training data JSONL file")
    parser.add_argument("--validation-data", type=str,
                       default="data/training/entity_validation_data.jsonl", 
                       help="Path to validation data JSONL file")
    parser.add_argument("--output-path", type=str,
                       default="data/models/ner_model",
                       help="Path to save the trained model")
    parser.add_argument("--epochs", type=int, default=5,
                       help="Number of training epochs")
    parser.add_argument("--batch-size", type=int, default=8,
                       help="Training batch size")
    parser.add_argument("--learning-rate", type=float, default=5e-5,
                       help="Learning rate")
    parser.add_argument("--base-model", type=str,
                       default="bert-base-uncased",
                       help="Base model to use")
    parser.add_argument("--no-eval", action="store_true",
                       help="Skip evaluation during training")
    
    args = parser.parse_args()
    
    # Initialize trainer
    trainer = EntityExtractorTrainer(base_model=args.base_model)
    
    # Start training
    try:
        result = trainer.train(
            training_data_path=args.training_data,
            validation_data_path=args.validation_data,
            output_path=args.output_path,
            num_epochs=args.epochs,
            batch_size=args.batch_size,
            learning_rate=args.learning_rate,
            skip_eval=args.no_eval
        )
        
        print("\n" + "="*50)
        print("[SUCCESS] TRAINING COMPLETED SUCCESSFULLY!")
        print("="*50)
        print(f"[FOLDER] Model saved to: {result['model_path']}")
        print(f"[TAG] Entity types: {len(result['entity_types'])}")
        print(f"[CHART] Training samples: {result['training_samples']}")
        print(f"[CHART] Validation samples: {result['validation_samples']}")
        print(f"[REFRESH] Epochs: {result['epochs']}")
        if result.get('onnx_path'):
            print(f"[OPTIMIZED] ONNX model: {result['onnx_path']}")
        else:
            print(f"[WARNING] ONNX optimization failed")
        print("="*50)
        
    except Exception as e:
        logger.error(f"Training failed: {e}")
        print(f"\n[ERROR] Training failed: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    exit(main())
