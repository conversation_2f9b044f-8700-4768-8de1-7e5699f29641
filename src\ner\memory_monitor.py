#!/usr/bin/env python3
"""
Memory monitoring utility for NER training in Kaggle environments.
"""

import gc
import logging
import psutil
import time
from typing import Dict, Any
import torch

logger = logging.getLogger(__name__)

class MemoryMonitor:
    """Monitor memory usage during training."""
    
    def __init__(self):
        self.start_time = time.time()
        self.peak_memory = 0
        self.peak_gpu_memory = 0
        
    def get_memory_info(self) -> Dict[str, Any]:
        """Get current memory information."""
        # CPU memory
        process = psutil.Process()
        cpu_memory_mb = process.memory_info().rss / 1024 / 1024
        cpu_memory_percent = process.memory_percent()
        
        # System memory
        system_memory = psutil.virtual_memory()
        system_memory_mb = system_memory.used / 1024 / 1024
        system_memory_percent = system_memory.percent
        
        info = {
            'cpu_memory_mb': cpu_memory_mb,
            'cpu_memory_percent': cpu_memory_percent,
            'system_memory_mb': system_memory_mb,
            'system_memory_percent': system_memory_percent,
            'timestamp': time.time() - self.start_time
        }
        
        # GPU memory if available
        if torch.cuda.is_available():
            gpu_memory_allocated = torch.cuda.memory_allocated() / 1024 / 1024
            gpu_memory_reserved = torch.cuda.memory_reserved() / 1024 / 1024
            gpu_memory_total = torch.cuda.get_device_properties(0).total_memory / 1024 / 1024
            
            info.update({
                'gpu_memory_allocated_mb': gpu_memory_allocated,
                'gpu_memory_reserved_mb': gpu_memory_reserved,
                'gpu_memory_total_mb': gpu_memory_total,
                'gpu_memory_percent': (gpu_memory_allocated / gpu_memory_total) * 100
            })
            
            # Track peak GPU memory
            if gpu_memory_allocated > self.peak_gpu_memory:
                self.peak_gpu_memory = gpu_memory_allocated
        
        # Track peak CPU memory
        if cpu_memory_mb > self.peak_memory:
            self.peak_memory = cpu_memory_mb
            
        return info
    
    def log_memory_usage(self, context: str = ""):
        """Log current memory usage."""
        info = self.get_memory_info()
        
        log_msg = f"[MEMORY] {context} - "
        log_msg += f"CPU: {info['cpu_memory_mb']:.1f}MB ({info['cpu_memory_percent']:.1f}%) "
        log_msg += f"System: {info['system_memory_mb']:.1f}MB ({info['system_memory_percent']:.1f}%)"
        
        if 'gpu_memory_allocated_mb' in info:
            log_msg += f" GPU: {info['gpu_memory_allocated_mb']:.1f}MB ({info['gpu_memory_percent']:.1f}%)"
        
        logger.info(log_msg)
        
        return info
    
    def check_memory_threshold(self, cpu_threshold: float = 80.0, gpu_threshold: float = 85.0) -> bool:
        """Check if memory usage exceeds thresholds."""
        info = self.get_memory_info()
        
        cpu_exceeded = info['cpu_memory_percent'] > cpu_threshold
        gpu_exceeded = False
        
        if 'gpu_memory_percent' in info:
            gpu_exceeded = info['gpu_memory_percent'] > gpu_threshold
        
        if cpu_exceeded or gpu_exceeded:
            logger.warning(f"Memory threshold exceeded - CPU: {info['cpu_memory_percent']:.1f}% "
                         f"(threshold: {cpu_threshold}%), GPU: {info.get('gpu_memory_percent', 0):.1f}% "
                         f"(threshold: {gpu_threshold}%)")
            return True
        
        return False
    
    def cleanup_memory(self):
        """Perform memory cleanup."""
        logger.info("Performing memory cleanup...")
        
        # Python garbage collection
        collected = gc.collect()
        logger.info(f"Garbage collected {collected} objects")
        
        # GPU memory cleanup
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            logger.info("Cleared GPU cache")
        
        # Log memory after cleanup
        self.log_memory_usage("After cleanup")
    
    def get_peak_memory_usage(self) -> Dict[str, float]:
        """Get peak memory usage statistics."""
        return {
            'peak_cpu_memory_mb': self.peak_memory,
            'peak_gpu_memory_mb': self.peak_gpu_memory,
            'runtime_seconds': time.time() - self.start_time
        }

def monitor_training_memory(func):
    """Decorator to monitor memory usage during training."""
    def wrapper(*args, **kwargs):
        monitor = MemoryMonitor()
        monitor.log_memory_usage("Training start")
        
        try:
            result = func(*args, **kwargs)
            monitor.log_memory_usage("Training completed")
            
            # Log peak usage
            peaks = monitor.get_peak_memory_usage()
            logger.info(f"Peak memory usage - CPU: {peaks['peak_cpu_memory_mb']:.1f}MB, "
                       f"GPU: {peaks['peak_gpu_memory_mb']:.1f}MB, "
                       f"Runtime: {peaks['runtime_seconds']:.1f}s")
            
            return result
            
        except Exception as e:
            monitor.log_memory_usage("Training failed")
            monitor.cleanup_memory()
            raise e
    
    return wrapper

class KaggleMemoryOptimizer:
    """Kaggle-specific memory optimization utilities."""
    
    @staticmethod
    def optimize_for_kaggle():
        """Apply Kaggle-specific memory optimizations."""
        logger.info("Applying Kaggle memory optimizations...")
        
        # Set environment variables for memory optimization
        import os
        os.environ['PYTORCH_CUDA_ALLOC_CONF'] = 'max_split_size_mb:128'
        os.environ['CUDA_LAUNCH_BLOCKING'] = '1'
        
        # Reduce PyTorch memory fragmentation
        if torch.cuda.is_available():
            torch.backends.cudnn.benchmark = False
            torch.backends.cudnn.deterministic = True
            
        logger.info("Kaggle optimizations applied")
    
    @staticmethod
    def get_kaggle_memory_limits():
        """Get Kaggle environment memory limits."""
        # Kaggle typically provides 16GB GPU memory and 30GB RAM
        return {
            'gpu_memory_gb': 16,
            'cpu_memory_gb': 30,
            'recommended_gpu_usage_percent': 80,
            'recommended_cpu_usage_percent': 75
        }
    
    @staticmethod
    def calculate_optimal_batch_size(model_size_mb: float, sequence_length: int = 256) -> int:
        """Calculate optimal batch size for Kaggle environment."""
        limits = KaggleMemoryOptimizer.get_kaggle_memory_limits()
        
        # Estimate memory per sample (rough calculation)
        memory_per_sample_mb = (model_size_mb * 0.1) + (sequence_length * 0.01)
        
        # Available memory for training (80% of GPU memory)
        available_memory_mb = limits['gpu_memory_gb'] * 1024 * 0.8
        
        # Calculate batch size
        optimal_batch_size = int(available_memory_mb / memory_per_sample_mb)
        
        # Clamp to reasonable range
        optimal_batch_size = max(1, min(optimal_batch_size, 16))
        
        logger.info(f"Calculated optimal batch size: {optimal_batch_size} "
                   f"(model: {model_size_mb}MB, seq_len: {sequence_length})")
        
        return optimal_batch_size

# Example usage
if __name__ == "__main__":
    # Test memory monitoring
    monitor = MemoryMonitor()
    monitor.log_memory_usage("Initial")
    
    # Apply Kaggle optimizations
    KaggleMemoryOptimizer.optimize_for_kaggle()
    
    # Get optimal batch size
    batch_size = KaggleMemoryOptimizer.calculate_optimal_batch_size(500, 256)
    print(f"Recommended batch size: {batch_size}")
