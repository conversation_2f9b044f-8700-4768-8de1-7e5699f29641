export interface User {
  id: string;
  email: string;
  fullName: string;
  employeeId?: string;
  isLoggedIn: boolean;
}

export interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  files?: FileAttachment[];
  sender?: 'user' | 'assistant';
  type?: 'normal' | 'clarification';
  isEditing?: boolean;
  editedAt?: Date;
  originalContent?: string;
  metadata?: {
    clarification_type?: string;
    selected_text?: string;
    target_phrase?: string;
    original_message_preview?: string;
    escalated?: boolean;
    autoResolved?: boolean;
    escalationTriggeredAt?: string;
    autoResolvedAt?: string;
  };
}

export interface FileAttachment {
  id: string;
  name: string;
  type: string;
  size: number;
  url?: string;
  preview?: string;
  file?: File;
}

export interface ChatSession {
  id: string;
  title: string;
  messages?: Message[];
  createdAt: Date;
  updatedAt: Date;
  isArchived?: boolean;
  messageCount?: number;
}

export interface EscalationForm {
  hrPerson: string;
  issueType: 'policy' | 'benefits' | 'workplace' | 'compensation' | 'other';
  issueDescription: string;
  priority: 'low' | 'medium' | 'high' | 'urgent';
  isAnonymous?: boolean; // Added for anonymous escalation
  user?: User | null;    // Store user details for super admin
  hrEmail?: string;      // Email of selected HR rep
}

export interface ThemeMode {
  mode: 'light' | 'dark' | 'system';
}

export interface AppSettings {
  theme: ThemeMode;
  language: string;
  notifications: boolean;
}

export interface SpeechRecognitionState {
  isListening: boolean;
  isSupported: boolean;
  transcript: string;
  error?: string;
}

export interface ModalState {
  login: boolean;
  register: boolean;
  settings: boolean;
  voice: boolean;
  filePreview: boolean;
  escalation: boolean;
  archivedChats: boolean;
  twoFA: boolean;
}

export interface SidebarState {
  isCollapsed: boolean;
  isVisible: boolean;
}
