#!/usr/bin/env python3
"""
Robust NER training script with comprehensive error handling and optimizations.
Designed to work reliably in Kaggle and other constrained environments.
"""

import json
import logging
import argparse
import os
import gc
import time
import traceback
from pathlib import Path
from typing import Dict, List, Any, Optional

# Environment setup
os.environ["WANDB_DISABLED"] = "true"
os.environ["WANDB_MODE"] = "disabled"
os.environ["TOKENIZERS_PARALLELISM"] = "false"
os.environ["OMP_NUM_THREADS"] = "1"
os.environ["PYTORCH_JIT"] = "0"

import warnings
warnings.filterwarnings("ignore")

import torch
from span_marker import SpanMarkerModel, Trainer as SpanMarkerTrainer
from transformers import TrainingArguments
from datasets import Dataset

# Import our utilities
try:
    from .memory_monitor import MemoryMonitor, KaggleMemoryOptimizer, monitor_training_memory
    from .validate_and_fix_data import NERDataValidator
except ImportError:
    # Fallback if imports fail
    class MemoryMonitor:
        def log_memory_usage(self, context): pass
        def cleanup_memory(self): pass
    
    class KaggleMemoryOptimizer:
        @staticmethod
        def optimize_for_kaggle(): pass
        @staticmethod
        def calculate_optimal_batch_size(model_size, seq_len): return 4
    
    def monitor_training_memory(func): return func
    
    class NERDataValidator:
        def validate_file(self, input_path, output_path=None, max_samples=None):
            return {'summary': {'valid_samples': 1000}}

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RobustNERTrainer:
    """Robust NER trainer with comprehensive error handling."""
    
    def __init__(self, config: Dict[str, Any]):
        self.config = config
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.is_kaggle = self._detect_kaggle()
        self.memory_monitor = MemoryMonitor()
        
        logger.info(f"Device: {self.device}, Kaggle: {self.is_kaggle}")
        
        # Apply optimizations
        if self.is_kaggle:
            KaggleMemoryOptimizer.optimize_for_kaggle()
        
        self.memory_monitor.log_memory_usage("Initialization")
    
    def _detect_kaggle(self) -> bool:
        """Detect Kaggle environment."""
        indicators = [
            os.path.exists('/kaggle'),
            'KAGGLE_KERNEL_RUN_TYPE' in os.environ,
            '/kaggle' in os.getcwd()
        ]
        return any(indicators)
    
    def prepare_data(self) -> Dataset:
        """Prepare and validate training data."""
        logger.info("Preparing training data...")
        
        # Validate and clean data first
        validator = NERDataValidator()
        cleaned_data_path = self.config['data_path'].replace('.jsonl', '_cleaned.jsonl')
        
        try:
            report = validator.validate_file(
                self.config['data_path'],
                cleaned_data_path,
                max_samples=self.config.get('max_samples')
            )
            
            if report['summary']['valid_samples'] == 0:
                raise ValueError("No valid training samples found")
            
            logger.info(f"Data validation: {report['summary']['valid_samples']} valid samples")
            
        except Exception as e:
            logger.warning(f"Data validation failed: {e}. Using original data.")
            cleaned_data_path = self.config['data_path']
        
        # Load cleaned data
        training_data = []
        with open(cleaned_data_path, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                if self.config.get('max_samples') and len(training_data) >= self.config['max_samples']:
                    break
                
                try:
                    data = json.loads(line.strip())
                    text = data['text']
                    entities = data['entities']
                    
                    # Skip very long texts for memory optimization
                    if len(text.split()) > self.config.get('max_text_length', 100):
                        continue
                    
                    # Simple tokenization and BIO conversion
                    tokens = text.split()
                    ner_tags = ["O"] * len(tokens)
                    
                    # Process entities
                    for entity in entities:
                        if isinstance(entity, list) and len(entity) >= 3:
                            start, end, label = entity[0], entity[1], entity[2]
                            
                            # Find overlapping tokens (simplified)
                            words_before_start = len(text[:start].split())
                            words_in_entity = len(text[start:end].split())
                            
                            for i in range(words_before_start, min(words_before_start + words_in_entity, len(tokens))):
                                if i < len(ner_tags):
                                    if i == words_before_start:
                                        ner_tags[i] = f"B-{label}"
                                    else:
                                        ner_tags[i] = f"I-{label}"
                    
                    training_data.append({
                        "tokens": tokens,
                        "ner_tags": ner_tags
                    })
                    
                except Exception as e:
                    logger.warning(f"Error processing line {line_num}: {e}")
                    continue
        
        if not training_data:
            raise ValueError("No training data loaded")
        
        logger.info(f"Loaded {len(training_data)} training samples")
        
        # Create dataset
        dataset = Dataset.from_list(training_data)
        self.memory_monitor.log_memory_usage("Data loaded")
        
        return dataset
    
    def get_labels(self, dataset: Dataset) -> List[str]:
        """Extract unique labels from dataset."""
        labels = set()
        for item in dataset:
            for tag in item["ner_tags"]:
                labels.add(tag)
        
        labels = sorted(list(labels))
        logger.info(f"Found {len(labels)} unique labels")
        return labels
    
    def create_model(self, labels: List[str]) -> SpanMarkerModel:
        """Create and initialize the model."""
        logger.info("Creating SpanMarker model...")
        
        # Choose model based on environment
        if self.is_kaggle:
            base_model = "distilbert-base-uncased"  # Smaller model for Kaggle
            max_length = 256
            entity_max_length = 8
        else:
            base_model = self.config.get('base_model', 'bert-base-uncased')
            max_length = self.config.get('max_length', 512)
            entity_max_length = self.config.get('entity_max_length', 12)
        
        try:
            model = SpanMarkerModel.from_pretrained(
                base_model,
                labels=labels,
                model_max_length=max_length,
                entity_max_length=entity_max_length,
                mean_resizing=False,
                ignore_mismatched_sizes=True
            )
            
            if self.device == "cuda":
                model = model.cuda()
                torch.cuda.empty_cache()
            
            logger.info(f"Model created successfully: {base_model}")
            self.memory_monitor.log_memory_usage("Model created")
            
            return model
            
        except Exception as e:
            logger.error(f"Model creation failed: {e}")
            raise
    
    def get_training_args(self) -> TrainingArguments:
        """Get optimized training arguments."""
        # Calculate optimal batch size
        if self.is_kaggle:
            batch_size = KaggleMemoryOptimizer.calculate_optimal_batch_size(500, 256)
            batch_size = min(batch_size, 4)  # Conservative for Kaggle
        else:
            batch_size = self.config.get('batch_size', 8)
        
        logger.info(f"Using batch size: {batch_size}")
        
        # Environment-specific arguments
        if self.is_kaggle:
            args = TrainingArguments(
                output_dir=self.config['output_path'],
                learning_rate=self.config.get('learning_rate', 3e-5),
                per_device_train_batch_size=batch_size,
                per_device_eval_batch_size=batch_size,
                num_train_epochs=self.config.get('epochs', 3),
                warmup_ratio=0.05,
                weight_decay=0.01,
                fp16=torch.cuda.is_available(),
                gradient_accumulation_steps=1,
                dataloader_num_workers=0,
                dataloader_pin_memory=False,
                save_strategy="no",
                eval_strategy="no",
                logging_steps=200,
                save_total_limit=1,
                load_best_model_at_end=False,
                report_to="none",
                remove_unused_columns=False,
                lr_scheduler_type="linear",
                warmup_steps=50,
                max_grad_norm=1.0,
                group_by_length=False,
                dataloader_drop_last=False,
                logging_first_step=False,
                disable_tqdm=False,
                skip_memory_metrics=True,
            )
        else:
            args = TrainingArguments(
                output_dir=self.config['output_path'],
                learning_rate=self.config.get('learning_rate', 5e-5),
                per_device_train_batch_size=batch_size,
                per_device_eval_batch_size=batch_size,
                num_train_epochs=self.config.get('epochs', 5),
                warmup_ratio=0.1,
                weight_decay=0.01,
                fp16=torch.cuda.is_available() and torch.cuda.get_device_capability()[0] < 8,
                bf16=torch.cuda.is_available() and torch.cuda.get_device_capability()[0] >= 8,
                gradient_accumulation_steps=2,
                dataloader_num_workers=2,
                dataloader_pin_memory=True,
                save_strategy="epoch",
                eval_strategy="no",  # Skip eval for simplicity
                logging_steps=100,
                save_total_limit=2,
                load_best_model_at_end=False,
                report_to="none",
                remove_unused_columns=False,
                lr_scheduler_type="cosine",
                warmup_steps=100,
                max_grad_norm=1.0,
                group_by_length=True,
                dataloader_drop_last=True,
                logging_first_step=True,
            )
        
        return args
    
    @monitor_training_memory
    def train(self) -> Dict[str, Any]:
        """Main training function with comprehensive error handling."""
        start_time = time.time()
        
        try:
            # Prepare data
            dataset = self.prepare_data()
            labels = self.get_labels(dataset)
            
            # Create model
            model = self.create_model(labels)
            
            # Get training arguments
            args = self.get_training_args()
            
            # Create output directory
            output_dir = Path(self.config['output_path'])
            output_dir.mkdir(parents=True, exist_ok=True)
            
            # Create trainer
            trainer = SpanMarkerTrainer(
                model=model,
                train_dataset=dataset,
                args=args
            )
            
            # Monitor memory before training
            self.memory_monitor.log_memory_usage("Before training")
            
            # Train with checkpointing
            logger.info("Starting training...")
            trainer.train()
            
            # Save model
            logger.info(f"Saving model to {output_dir}")
            trainer.save_model(str(output_dir))
            
            # Save metadata
            metadata = {
                'labels': labels,
                'training_samples': len(dataset),
                'epochs': self.config.get('epochs', 3),
                'training_time': time.time() - start_time,
                'model_type': 'spanmarker',
                'environment': 'kaggle' if self.is_kaggle else 'local',
                'success': True
            }
            
            with open(output_dir / "training_metadata.json", 'w') as f:
                json.dump(metadata, f, indent=2)
            
            # Cleanup
            del model, trainer, dataset
            self.memory_monitor.cleanup_memory()
            
            logger.info(f"Training completed successfully in {metadata['training_time']:.1f}s")
            return metadata
            
        except Exception as e:
            logger.error(f"Training failed: {e}")
            logger.error(f"Traceback: {traceback.format_exc()}")
            
            # Cleanup on failure
            self.memory_monitor.cleanup_memory()
            
            return {
                'success': False,
                'error': str(e),
                'training_time': time.time() - start_time
            }

def main():
    """Main function."""
    parser = argparse.ArgumentParser(description="Robust NER training")
    parser.add_argument("--data-path", type=str, required=True,
                       help="Path to training data JSONL file")
    parser.add_argument("--output-path", type=str, required=True,
                       help="Output model directory")
    parser.add_argument("--max-samples", type=int, default=5000,
                       help="Maximum training samples")
    parser.add_argument("--epochs", type=int, default=3,
                       help="Number of epochs")
    parser.add_argument("--batch-size", type=int,
                       help="Batch size (auto-calculated if not provided)")
    parser.add_argument("--learning-rate", type=float, default=3e-5,
                       help="Learning rate")
    
    args = parser.parse_args()
    
    config = {
        'data_path': args.data_path,
        'output_path': args.output_path,
        'max_samples': args.max_samples,
        'epochs': args.epochs,
        'batch_size': args.batch_size,
        'learning_rate': args.learning_rate,
        'max_text_length': 100,  # Skip very long texts
    }
    
    trainer = RobustNERTrainer(config)
    result = trainer.train()
    
    if result['success']:
        print("\n🎉 Training completed successfully!")
        print(f"📁 Model saved to: {config['output_path']}")
        print(f"⏱️  Training time: {result['training_time']:.1f}s")
    else:
        print(f"\n❌ Training failed: {result['error']}")
        return 1
    
    return 0

if __name__ == "__main__":
    exit(main())
